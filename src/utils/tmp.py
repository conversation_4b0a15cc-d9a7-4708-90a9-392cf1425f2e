from funcs import get_raw_data, pre_process_data, process_data
from plots import Plotter


from plots import Plotter

plotter = Plotter()
train_data=pre_process_data(get_raw_data('12.3',2e5))
val_data=pre_process_data(get_raw_data('12.5',2e5))
plotter.plot_data_scatter(train_data, val_data, axis_dict={'x':0, 'y':3, 'z':8})


# E=200000
# train_data=pre_process_data(get_raw_data(f'12.3',E))
# val_data=pre_process_data(get_raw_data(f'12.5',E))

# n_train_data, n_val_data, norm_params = process_data(train_data, val_data)
