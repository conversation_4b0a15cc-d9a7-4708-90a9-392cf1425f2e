"""
This module contains plotting utilities for the project.

Date: 2025-07-09
Author: <PERSON><PERSON><PERSON>
"""

#%% ---- Import Required Libraries ----
import matplotlib.pyplot as plt
import numpy as np
import plotly.graph_objects as go

from matplotlib.ticker import ScalarFormatter
formatter = ScalarFormatter(useMathText=True)
formatter.set_scientific(True)

class Plotter:
    def __init__ (self):
        plt.rcParams["font.family"] = "serif"
        plt.rc('axes.formatter', use_mathtext=True)
        plt.rcParams["font.serif"] = "cmr10"
        plt.rcParams['font.size']=12

    def plot_data_scatter (self, train_data, val_data=None, test_data=None, axis_dict:dict={'x':0, 'y':1, 'z':2}):
        """Plot a 3D scatter plot of the data.

        Parameters:
        - train_data (array-like): Training data.
        - val_data (array-like): Validation data (optional).
        - test_data (array-like): Test data (optional).
        - axis_dict (dict): Dictionary specifying the indices of the features to plot on each axis.
                NOTE: The values and their corresponding titles are: 0: eps_pl_t, 1: s_t, 2: psi_max_t
        """

        axis_dict = axis_dict
        x_index = axis_dict['x']
        y_index = axis_dict['y']
        z_index = axis_dict['z']

        axis_titles = {0: 'eps_pl_I1', 1: 'eps_pl_I2', 2: 'eps_pl_I3',
                       3: 's_dev_11', 4: 's_dev_22', 5: 's_dev_12', 6: 's_dev_23', 7: 's_dev_13',
                       8: 'psi_max'}

        fig = go.Figure()
        fig.add_trace(go.Scatter3d(x=train_data[:, x_index], y=train_data[:, y_index], z=train_data[:,  z_index],
                                mode='markers', marker=dict(size=4, color='blue', opacity=0.8), name='Training'))
        if val_data is not None:
            fig.add_trace(go.Scatter3d(x=val_data[:, x_index], y=val_data[:, y_index], z=val_data[:, z_index],
                                    mode='markers', marker=dict(size=4, color='red', opacity=0.8), name='Validation'))
        if test_data is not None:
            fig.add_trace(go.Scatter3d(x=test_data[:, x_index], y=test_data[:, y_index], z=test_data[:, z_index],
                                    mode='markers', marker=dict(size=4, color='green', opacity=0.8), name='Test'))
        fig.update_layout(scene=dict(xaxis_title=axis_titles[x_index], yaxis_title=axis_titles[y_index], zaxis_title=axis_titles[z_index]), width=752, height=750, showlegend=True)

        fig.show()

    def plot_data_scatter_matplotlib (self, train_data, val_data=None, test_data=None, axis_dict:dict={'x':0, 'y':1, 'z':2}):
        """Plot a 3D scatter plot of the data.

        Parameters:
        - train_data (array-like): Training data.
        - val_data (array-like): Validation data (optional).
        - test_data (array-like): Test data (optional).
        - axis_dict (dict): Dictionary specifying the indices of the features to plot on each axis.
                NOTE: The values and their corresponding titles are: 0: eps_pl_t, 1: s_t, 2: psi_max_t
        """

        x_index = axis_dict['x']
        y_index = axis_dict['y']
        z_index = axis_dict['z']

        axis_titles = {0: 'eps_pl_I1', 1: 'eps_pl_I2', 2: 'eps_pl_I3',
                       3: 's_dev_11', 4: 's_dev_22', 5: 's_dev_12', 6: 's_dev_23', 7: 's_dev_13',
                       8: 'psi_max'}

        fig = plt.figure()
        ax = fig.add_subplot(111, projection='3d')
        ax.scatter(train_data[:, x_index], train_data[:, y_index], train_data[:, z_index], c='b', marker='o', label='Training')
        if val_data is not None:
            ax.scatter(val_data[:, x_index], val_data[:, y_index], val_data[:, z_index], c='r', marker='o', label='Validation')
        if test_data is not None:
            ax.scatter(test_data[:, x_index], test_data[:, y_index], test_data[:, z_index], c='g', marker='o', label='Test')
        ax.set_xlabel(axis_titles[x_index])
        ax.set_ylabel(axis_titles[y_index])
        ax.set_zlabel(axis_titles[z_index])
        ax.legend()
        plt.show()