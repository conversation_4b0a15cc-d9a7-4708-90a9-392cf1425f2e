from plots import Plotter
plotter = Plotter()

from funcs import get_raw_data, compute_psi, compute_invariants, compute_deviatoric, compute_eps_pl, pre_process_data
import numpy as np
raw_data = get_raw_data('12.5',2e5)
pre_processed_data = pre_process_data(raw_data)
val_data = pre_process_data(get_raw_data('12.3',2e5))


from funcs import process_data

n_data1, n_data2, norm_params = process_data(pre_processed_data, pre_processed_data)


print(norm_params[3:4])
print(len(norm_params[3:4]))

plotter.plot_data_scatter(pre_processed_data, val_data, axis_dict={'x':0, 'y':3, 'z':8})

